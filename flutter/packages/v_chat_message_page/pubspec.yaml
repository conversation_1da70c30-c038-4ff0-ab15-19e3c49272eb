name: v_chat_message_page
description: v chat sdk message page ui package this package is part of v chat sdk and cant be used alone
version: 1.1.0
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/hatemragab/v_chat_sdk/issues
repository: https://github.com/hatemragab/v_chat_sdk/tree/master/v_ui_packages/v_chat_message_page
publish_to: none

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  v_chat_sdk_core:
    path: ../v_chat_sdk_core
  super_up_core:
    path: ../super_up_core
  v_chat_input_ui:
    path: ../v_chat_input_ui
  v_chat_room_page:
    path: ../v_chat_room_page
  s_translation:
    path: ../s_translation
  v_chat_voice_player: ^3.0.0
  v_chat_media_editor:
    path: ../v_chat_media_editor
  map_launcher: ^3.5.0
  scroll_to_index: ^3.0.1
  flutter_parsed_text: ^2.2.1
  open_filex: ^4.5.0
  share_plus: ^10.1.1
  flutter_cache_manager: ^3.4.1
  meta: ^1.15.0
  flutter_dropzone: '>=4.0.0 <4.1.0'
  flutter_link_previewer: ^3.2.2
  v_platform: ^2.1.4
  pasteboard: ^0.3.0
  universal_html: ^2.2.4
  textless:
    git:
      url: https://github.com/hatemragab/textless.git
  adaptive_dialog: ^2.2.1+2
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.1
  flutter_advanced_avatar: ^1.5.0
  timeago: ^3.7.0
  intl: ^0.20.2
  collection: ^1.18.0
  phosphor_flutter: ^2.1.0
  http: ^1.2.2
  uuid: ^4.5.1
  path_provider: ^2.1.5
  photo_view: ^0.15.0
  chewie: ^1.8.5
  video_player: ^2.9.2
  shared_preferences: ^2.3.3
  permission_handler: ^11.3.1
  stop_watch_timer: ^3.1.1
  flutter_ringtone_player: ^4.0.0+3
  wakelock_plus: ^1.2.8
  oktoast: ^3.4.0
  get_it: ^8.0.2
  any_link_preview: ^3.0.2
  agora_rtc_engine: ^6.5.0
  flutter_blurhash: ^0.8.2
  background_downloader:  ^8.8.0
  connectivity_plus: ^6.1.0
  just_audio: ^0.10.4
  path: ^1.9.0
  audio_service: ^0.18.18
dev_dependencies:
  flutter_lints: ^5.0.0
  lints: ^5.0.0

flutter:
  assets:
    - assets/dialing.mp3
platforms:
  android:
  ios:
  web:
  windows:
  macos:
