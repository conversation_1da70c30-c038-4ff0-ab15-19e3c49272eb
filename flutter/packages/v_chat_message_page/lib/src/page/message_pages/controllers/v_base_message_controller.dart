// Copyright 2023, the hate<PERSON><PERSON><PERSON> project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:uuid/uuid.dart';
import 'package:v_chat_media_editor/v_chat_media_editor.dart';
import 'package:v_chat_message_page/src/page/message_pages/controllers/v_message_item_controller.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../v_chat_message_page.dart';
import '../pasteboard/file_convertor.dart';
import '../pasteboard/pasteboard.dart';
import '../states/input_state_controller.dart';
import '../states/message_state/message_state_controller.dart';

abstract class VBaseMessageController extends MessageStateController
    with StreamMix {
  final focusNode = FocusNode();
  final vConfig = VChatController.I.vChatConfig;
  final InputStateController inputStateController;
  final VMessageItemController itemController;
  final events = VEventBusSingleton.vEventBus;
  final VMessageConfig vMessageConfig;
  final uuid = const Uuid();

  VBaseMessageController({
    required super.vRoom,
    required super.messageProvider,
    required super.scrollController,
    required this.inputStateController,
    required this.vMessageConfig,
    required this.itemController,
  }) {
    messageProvider.setSeen(roomId);
    VRoomTracker.instance.addToOpenRoom(roomId: roomId);
    _removeAllNotifications();
    _setUpVoiceController();
    _initMessagesStreams();
    _setUpPasteboardStreamListener();
    _listenToAppLifecycle();
  }

  StreamSubscription? clipboardSubscription;
  StreamSubscription? appLifecycleSubscription;
  late final VVoicePlayerController voiceControllers;

  String get roomId => vRoom.id;
  final IPasteboard pasteboard = Pasteboard(FileConvertor());

  void onTitlePress(BuildContext context);

  void _setUpPasteboardStreamListener() {
    if (kIsWeb) clipboardSubscription = pasteboard.pasteBoardListener(onPaste);
  }

  void _listenToAppLifecycle() {
    appLifecycleSubscription =
        VEventBusSingleton.vEventBus.on<VAppLifeCycle>().listen((event) {
      if (event.isGoBackground) {
        // Pause all voice messages when app goes to background
        voiceControllers.pauseAll();
      }
    });
  }

  @override
  void close() {
    focusNode.dispose();
    inputStateController.close();
    voiceControllers.close();
    clipboardSubscription?.cancel();
    appLifecycleSubscription?.cancel();
    VRoomTracker.instance.closeOpenedRoom(roomId);
    closeStreamMix();
    super.close();
  }

  void _removeAllNotifications() async {
    await VChatController.I.vChatConfig.cleanNotifications();
  }

  void onOpenSearch() {
    inputStateController.hide();
  }

  void onCloseSearch() {
    inputStateController.unHide();
    resetMessages();
  }

  void onSearch(String value) async {
    messageSearch(value);
  }

  void onMessageLongTap(BuildContext context, VBaseMessage message) {
    return itemController.onMessageItemLongPress(
      message,
      vRoom,
      setReply,
    );
  }

  void onSubmitMedia(
    BuildContext context,
    List<VPlatformFile> files,
  ) async {
    final result = await context.toPage(VMediaEditorView(
      files: files,
      config: VMediaEditorConfig(
        imageQuality: vMessageConfig.compressImageQuality,
      ),
    )) as VMediaEditorResult?;

    if (result == null) return;

    final messageText = result.messageText.trim();

    for (var media in result.mediaFiles) {
      if (media is VMediaImageRes) {
        final localMsg = VImageMessage.buildMessage(
          roomId: vRoom.id,
          data: VMessageImageData.fromMap(media.data.toMap()),
          content: messageText.isNotEmpty ? messageText : null,
        );
        _onSubmitSendMessage(localMsg);
      } else if (media is VMediaVideoRes) {
        final localMsg = VVideoMessage.buildMessage(
          data: VMessageVideoData.fromMap(media.data.toMap()),
          roomId: vRoom.id,
          content: messageText.isNotEmpty ? messageText : null,
        );
        _onSubmitSendMessage(localMsg);
      }
    }
    scrollDown();
  }

  Future<void> onPaste(List<VPlatformFile> files) async {
    final result = await context.toPage(VMediaEditorView(
      files: files,
      config: VMediaEditorConfig(
        imageQuality: vMessageConfig.compressImageQuality,
      ),
    )) as VMediaEditorResult?;

    if (result == null || result.mediaFiles.isEmpty) return;

    final messageText = result.messageText.trim();

    for (final file in result.mediaFiles) {
      if (file is VMediaImageRes) {
        _onSubmitSendMessage(
          VImageMessage.buildMessage(
            roomId: roomId,
            data: VMessageImageData.fromMap(
              file.data.toMap(),
            ),
            content: messageText.isNotEmpty ? messageText : null,
          ),
        );
      }
      if (file is VMediaFileRes) {
        _onSubmitSendMessage(
          VFileMessage.buildMessage(
            roomId: roomId,
            data: VMessageFileData.fromMap(file.data.toMap()),
          ),
        );
      }
    }
  }

  void onSubmitVoice(VMessageVoiceData data) {
    final localMsg = VVoiceMessage.buildMessage(
      data: data,
      roomId: vRoom.id,
      content: VStringUtils.printDuration(data.durationObj),
    );
    _onSubmitSendMessage(localMsg);
    scrollDown();
  }

  void onSubmitFiles(List<VPlatformFile> files) {
    for (var file in files) {
      final localMsg = VFileMessage.buildMessage(
        data: VMessageFileData.fromMap(file.toMap()),
        roomId: vRoom.id,
      );
      _onSubmitSendMessage(localMsg);
      scrollDown();
    }
  }

  void onSubmitLocation(VLocationMessageData data) {
    final localMsg = VLocationMessage.buildMessage(
      data: data,
      roomId: vRoom.id,
    );
    _onSubmitSendMessage(localMsg);
    scrollDown();
  }

  void onStickerSelected(VSticker sticker) {
    // Find the sticker pack ID (for now, use the first pack that contains this sticker)
    String stickerPackId = 'default';
    final stickerPacks = VStickerData.getDefaultStickerPacks();
    for (final pack in stickerPacks) {
      if (pack.stickers.any((s) => s.id == sticker.id)) {
        stickerPackId = pack.id;
        break;
      }
    }

    final stickerData = {
      'type': 'sticker',
      'stickerId': sticker.id,
      'stickerPackId': stickerPackId,
      'assetPath': sticker.assetPath,
      'name': sticker.name,
      'emoji': sticker.emoji,
      'tags': sticker.tags,
    };

    final localMsg = VCustomMessage.buildMessage(
      roomId: vRoom.id,
      data: VCustomMsgData(data: stickerData),
      content: "Sticker: ${sticker.name}",
    );
    _onSubmitSendMessage(localMsg);
    scrollDown();
  }

  void onTypingChange(VRoomTypingEnum typing) {
    if (typing == VRoomTypingEnum.recording) {
      _stopVoicePlayer();
    }
    final model = VSocketRoomTypingModel(
      status: typing,
      roomId: vRoom.id,
    );
    messageProvider.emitTypingChanged(model);
  }

  Future<void> _onSubmitSendMessage(VBaseMessage localMsg) async {
    localMsg.replyTo = inputStateController.value.replyMsg;
    await VChatController.I.nativeApi.local.message.insertMessage(localMsg);
    VMessageUploaderQueue.instance.addToQueue(
      await MessageFactory.createUploadMessage(localMsg),
    );
    inputStateController.dismissReply();
  }

  void onSubmitText(String message, VLinkPreviewData? previewData) {
    final isEnable = vConfig.enableEndToEndMessageEncryption;
    final localMsg = VTextMessage.buildMessage(
      content: isEnable ? VMessageEncryption.encryptMessage(message) : message,
      isEncrypted: isEnable,
      linkAtt: previewData,
      roomId: vRoom.id,
    );
    scrollDown();
    _onSubmitSendMessage(localMsg);
  }

  void scrollDown() {
    scrollController.animateTo(
      0.0,
      curve: Curves.easeOut,
      duration: const Duration(milliseconds: 300),
    );
  }

  Future<void> onHighlightMessage(VBaseMessage message) async {
    final i = value.indexOf(message);
    if (i == -1) {
      final x = await loadMoreMessages();
      if (x == null || x.isEmpty) {
        return;
      }
      onHighlightMessage(message);
    } else {
      _highlightTo(i);
    }
  }

  void _highlightTo(int index) {
    scrollController.scrollToIndex(
      index,
      preferPosition: AutoScrollPosition.end,
      duration: const Duration(milliseconds: 500),
    );
    scrollController.highlight(index);
  }

  void setReply(VBaseMessage p1) {
    focusNode.requestFocus();
    if (p1.emitStatus.isServerConfirm) {
      inputStateController.setReply(p1);
    }
  }

  void dismissReply() {
    inputStateController.dismissReply();
  }

  void onReSend(VBaseMessage message) async {
    VMessageUploaderQueue.instance.addToQueue(
      await MessageFactory.createUploadMessage(message),
    );
  }

  ///set to each controller
  Future<List<MentionModel>> onMentionRequireSearch(
    BuildContext context,
    String query,
  );

  void _setUpVoiceController() {
    voiceControllers = VVoicePlayerController(
      (localId) {
        final index = value.indexWhere((e) => e.localId == localId);
        if (index == -1 || index == 0) {
          return null;
        }
        if (!value[index - 1].messageType.isVoice) {
          return null;
        }
        return value[index - 1].localId;
      },
    );

    // Update the global controller with current callback when this controller is active
    VGlobalVoiceController().updatePlayNextCallback(
      (localId) {
        final index = value.indexWhere((e) => e.localId == localId);
        if (index == -1 || index == 0) {
          return null;
        }
        if (!value[index - 1].messageType.isVoice) {
          return null;
        }
        return value[index - 1].localId;
      },
    );
  }

  ///----------------------------- Messages streams -----------------------------------------------------
  void _initMessagesStreams() {
    ///messages events
    streamsMix.addAll([
      events
          .on<VInsertMessageEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnNewMessage),
      events
          .on<VUpdateMessageEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnUpdateMessage),
      events
          .on<VDeleteMessageEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnDeleteMessage),
      events
          .on<VUpdateMessageDeliverEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnDeliverMessage),
      events
          .on<VUpdateMessageSeenEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnSeenMessage),
      events
          .on<VUpdateProgressMessageEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleUpdateProgress),
      events
          .on<VUpdateIsDownloadMessageEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleUpdateIsDownloading),
      events
          .on<VUpdateMessageStatusEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnUpdateMessageStatus),
      events
          .on<VUpdateMessageOneSeenEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnUpdateOneSeen),
      events
          .on<VUpdateMessageStarEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleUpdateStar),
      events
          .on<VUpdateMessageAllDeletedEvent>()
          .where((event) => event.roomId == vRoom.id)
          .listen(_handleOnAllDeleted),
    ]);
  }

  void _handleOnNewMessage(VInsertMessageEvent event) async {
    emitSeenFor(event.roomId);
    insertMessage(event.messageModel);
  }

  void _handleOnUpdateMessage(VUpdateMessageEvent event) async {
    updateMessage(event.messageModel);
  }

  void _handleOnDeleteMessage(VDeleteMessageEvent event) async {
    deleteMessage(event.localId);
  }

  void _handleOnDeliverMessage(VUpdateMessageDeliverEvent event) async {
    deliverAll(event.model);
  }

  void _handleOnSeenMessage(VUpdateMessageSeenEvent event) async {
    seenAll(event.model);
  }

  void _handleOnUpdateMessageStatus(VUpdateMessageStatusEvent event) async {
    updateMessageStatus(event.localId, event.emitState);
  }

  void _handleOnAllDeleted(VUpdateMessageAllDeletedEvent event) {
    updateMessageAllDeletedAt(event.localId, event.message.allDeletedAt);
  }

  void onGetClipboardImageBytes(Uint8List imageBytes) async {
    final result = await context.toPage(VMediaEditorView(
      files: [
        VPlatformFile.fromBytes(
            bytes: imageBytes.toList(), name: "${uuid.v4()}.png")
      ],
      config: VMediaEditorConfig(
        imageQuality: vMessageConfig.compressImageQuality,
      ),
    )) as VMediaEditorResult?;

    if (result == null || result.mediaFiles.isEmpty) return;

    final messageText = result.messageText.trim();

    for (final e in result.mediaFiles) {
      if (e is VMediaImageRes) {
        _onSubmitSendMessage(
          VImageMessage.buildMessage(
            roomId: roomId,
            data: VMessageImageData.fromMap(e.data.toMap()),
            content: messageText.isNotEmpty ? messageText : null,
          ),
        );
      }
    }
  }

  void _stopVoicePlayer() {
    voiceControllers.pauseAll();
  }

  void _handleUpdateStar(VUpdateMessageStarEvent event) {
    updateMessageStar(event.localId, event);
  }

  void _handleOnUpdateOneSeen(VUpdateMessageOneSeenEvent event) {
    updateMessageOneSeen(event.localId, event);
  }

  void _handleUpdateProgress(VUpdateProgressMessageEvent event) {
    updateDownloadProgress(event.localId, event.progress);
  }

  void _handleUpdateIsDownloading(VUpdateIsDownloadMessageEvent event) {
    updateIsDownloading(event.localId, event.isDownloading);
  }
}
