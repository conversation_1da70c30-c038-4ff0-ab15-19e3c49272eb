// Copyright 2023, the hatem<PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:collection/collection.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_chat_voice_player/v_chat_voice_player.dart';

/// Global voice controller singleton for managing voice playback across the app
class VGlobalVoiceController {
  static final VGlobalVoiceController _instance =
      VGlobalVoiceController._internal();
  factory VGlobalVoiceController() => _instance;
  VGlobalVoiceController._internal();

  final _voiceControllers = <VVoiceMessageController>[];
  String? Function(String localId)? _currentOnVoiceNeedToPlayNext;

  VVoiceMessageController? getById(String id) =>
      _voiceControllers.firstWhereOrNull((e) => e.id == id);

  VVoiceMessageController getVoiceController(VVoiceMessage voiceMessage,
      String? Function(String localId)? onVoiceNeedToPlayNext) {
    final oldController = getById(voiceMessage.localId);

    if (oldController != null) {
      // Update the callback for existing controller
      _currentOnVoiceNeedToPlayNext = onVoiceNeedToPlayNext;
      return oldController;
    }

    final controller = VVoiceMessageController(
      id: voiceMessage.localId,
      audioSrc: voiceMessage.data.fileSource,
      onComplete: (String localId) {
        final nextId = _currentOnVoiceNeedToPlayNext?.call(localId);
        if (nextId != null) {
          getById(nextId)?.initAndPlay();
        }
      },
      maxDuration: voiceMessage.data.durationObj,
      onPlaying: _onPlaying,
    );
    _voiceControllers.add(controller);
    _currentOnVoiceNeedToPlayNext = onVoiceNeedToPlayNext;
    return controller;
  }

  void _onPlaying(String id) {
    for (final controller in _voiceControllers) {
      if (controller.id != id) {
        controller.pausePlaying();
      }
    }
  }

  void pauseAll() {
    for (final c in _voiceControllers) {
      c.pausePlaying();
    }
  }

  void updatePlayNextCallback(String? Function(String localId)? callback) {
    _currentOnVoiceNeedToPlayNext = callback;
  }

  void disposeController(String id) {
    final controller = getById(id);
    if (controller != null) {
      controller.dispose();
      _voiceControllers.removeWhere((c) => c.id == id);
    }
  }

  void disposeAll() {
    for (final c in _voiceControllers) {
      c.dispose();
    }
    _voiceControllers.clear();
    _currentOnVoiceNeedToPlayNext = null;
  }
}

class VVoicePlayerController {
  final _globalController = VGlobalVoiceController();
  final String? Function(String localId) onVoiceNeedToPlayNext;

  VVoicePlayerController(this.onVoiceNeedToPlayNext);

  VVoiceMessageController? getById(String id) => _globalController.getById(id);

  VVoiceMessageController getVoiceController(VVoiceMessage voiceMessage) {
    return _globalController.getVoiceController(
        voiceMessage, onVoiceNeedToPlayNext);
  }

  void pauseAll() {
    _globalController.pauseAll();
  }

  void close() {
    // Don't dispose controllers on close, let them continue playing
    // Only update the callback to null for this specific controller
    _globalController.updatePlayNextCallback(null);
  }
}
