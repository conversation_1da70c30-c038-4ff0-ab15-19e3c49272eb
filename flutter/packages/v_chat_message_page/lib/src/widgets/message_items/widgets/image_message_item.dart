// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:ui';

import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../shared/constraint_image.dart';
import '../shared/download_upload_widgets/message_downloader_circular_widget.dart';
import '../shared/download_upload_widgets/message_downloader_widget.dart';

class ImageMessageItem extends StatelessWidget {
  final VImageMessage message;
  final BoxFit? fit;

  const ImageMessageItem({
    super.key,
    this.fit,
    required this.message,
  });

  void _navigateToImageViewer(VImageMessage message, BuildContext context) {
    VChatController.I.vNavigator.messageNavigator.toImageViewer(
      context,
      message.data.fileSource,
      !message.isOneSeen,
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasCustomText =
        message.realContent != "📷"; // VMessageConstants.thisContentIsImage

    if (!VPlatforms.isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () => _navigateToImageViewer(message, context),
            child: VConstraintImage(
              data: message.data,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(15),
            ),
          ),
          if (hasCustomText)
            Padding(
              padding: const EdgeInsets.only(top: 8.0, left: 4.0, right: 4.0),
              child: Text(
                message.realContent,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: !message.isFileDownloaded
              ? null
              : () => _navigateToImageViewer(message, context),
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (message.isFileDownloaded)
                VConstraintImage(
                  data: message.data,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(15),
                )
              else
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: ImageFiltered(
                    imageFilter: ImageFilter.blur(
                      sigmaX: 10,
                      sigmaY: 10,
                    ),
                    child: VConstraintImage(
                      data: message.data,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                ),

              ///download widgets
              if (message.isMessageHasProgress)
                MessageProgressCircularWidget(
                  downloadProgress: message.progress,
                  onCancel: () {
                    FileDownloader().cancelTaskWithId(message.localId);
                  },
                )
              else if (!message.isFileDownloaded)
                MessageDownloaderWidget(message: message),
            ],
          ),
        ),
        if (hasCustomText)
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 4.0, right: 4.0),
            child: Text(
              message.realContent,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }
}
