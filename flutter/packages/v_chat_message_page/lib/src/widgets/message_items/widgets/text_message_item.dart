// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';

import '../../../../v_chat_message_page.dart';

class TextMessageItem extends StatelessWidget {
  final String message;
  final TextStyle textStyle;
  final Function(String email) onEmailPress;
  final Function(BuildContext context, String userId) onMentionPress;
  final Function(String phone) onPhonePress;
  final Function(String link) onLinkPress;
  final bool isEdited;

  const TextMessageItem({
    super.key,
    required this.message,
    required this.textStyle,
    required this.onEmailPress,
    required this.onMentionPress,
    required this.onPhonePress,
    required this.onLinkPress,
    this.isEdited = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VTextParserWidget(
            text: message,
            textStyle: textStyle,
            enableTabs: true,
            mentionTextStyle: const TextStyle(color: Colors.blue),
            onEmailPress: onEmailPress,
            onLinkPress: onLinkPress,
            onPhonePress: onPhonePress,
            onMentionPress: (userId) => onMentionPress(context, userId),
          ),
          if (isEdited)
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                'edited',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
