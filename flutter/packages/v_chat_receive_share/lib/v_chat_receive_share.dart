// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

library v_chat_receive_share;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:share_handler/share_handler.dart';
import 'package:v_chat_media_editor/v_chat_media_editor.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

Future<void> vInitReceiveShareHandler() async {
  if (!VPlatforms.isMobile) return;

  final handler = ShareHandlerPlatform.instance;
  final SharedMedia? media = await handler.getInitialSharedMedia();
  if (media != null) {
    _handleOnNewShare(media);
  }
  handler.sharedMediaStream.listen(
    (SharedMedia media) async {
      _handleOnNewShare(media);
    },
  );
}

Future<void> _handleOnNewShare(SharedMedia media) async {
  await Future.delayed(const Duration(seconds: 1));
  final messages = <VBaseMessage>[];
  final pFiles = <VPlatformFile>[];
  final context = VChatController.I.navigationContext;
  if (media.content != null) {
    final roomsIds = await VChatController.I.vNavigator.roomNavigator
        .toForwardPage(context, null);
    if (roomsIds != null) {
      for (final roomId in roomsIds) {
        messages.add(
          VTextMessage.buildMessage(
            roomId: roomId,
            content: media.content!,
            isEncrypted: false,
            linkAtt: null,
          ),
        );
      }
    }
  } else if (media.attachments != null && media.attachments!.isNotEmpty) {
    for (final m in media.attachments!) {
      if (m == null) continue;
      m.path.replaceAll("file://", "");
      pFiles.add(
        VPlatformFile.fromPath(
          fileLocalPath: m.path,
        ),
      );
    }

    final roomsIds = await VChatController.I.vNavigator.roomNavigator
        .toForwardPage(context, null);

    if (roomsIds != null) {
      final result = await Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => VMediaEditorView(
                files: pFiles,
              ))) as VMediaEditorResult?;

      if (result == null) return;

      final messageText = result.messageText.trim();

      for (final roomId in roomsIds) {
        for (final file in result.mediaFiles) {
          if (file is VMediaImageRes) {
            messages.add(VImageMessage.buildMessage(
              roomId: roomId,
              data: VMessageImageData.fromMap(
                file.data.toMap(),
              ),
              content: messageText.isNotEmpty ? messageText : null,
            ));
          } else if (file is VMediaVideoRes) {
            messages.add(VVideoMessage.buildMessage(
              roomId: roomId,
              data: VMessageVideoData.fromMap(file.data.toMap()),
              content: messageText.isNotEmpty ? messageText : null,
            ));
          } else {
            messages.add(VFileMessage.buildMessage(
              roomId: roomId,
              data: VMessageFileData(fileSource: file.getVPlatformFile()),
            ));
          }
        }
      }
    }
  }
  if (messages.isNotEmpty) {
    for (final message in messages) {
      await VChatController.I.nativeApi.local.message.insertMessage(message);
      try {
        VMessageUploaderQueue.instance.addToQueue(
          await MessageFactory.createUploadMessage(message),
        );
      } catch (err) {
        if (kDebugMode) {
          print(err);
        }
      }
    }
  }
}
