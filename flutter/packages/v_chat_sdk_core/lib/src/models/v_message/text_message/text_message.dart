// Copyright 2023, the hate<PERSON><PERSON><PERSON> project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

class VTextMessage extends VBaseMessage {
  VTextMessage({
    required super.id,
    required super.senderId,
    required super.senderName,
    required super.senderImageThumb,
    required super.linkAtt,
    required super.emitStatus,
    required super.isEncrypted,
    required super.contentTr,
    required super.platform,
    required super.roomId,
    required super.content,
    required super.messageType,
    required super.localId,
    required super.createdAt,
    required super.updatedAt,
    required super.replyTo,
    required super.seenAt,
    required super.deliveredAt,
    required super.forwardId,
    required super.allDeletedAt,
    required super.parentBroadcastId,
    required super.isStared,
    super.isEdited = false,
  });

  VTextMessage.buildMessage({
    required super.content,
    required super.isEncrypted,
    required super.linkAtt,
    required super.roomId,
    super.forwardId,
    super.broadcastId,
    super.replyTo,
  }) : super.buildMessage(messageType: VMessageType.text);

  VTextMessage.fromRemoteMap(super.map) : super.fromRemoteMap();

  VTextMessage.fromLocalMap(super.map) : super.fromLocalMap();

  @override
  bool operator ==(Object other) =>
      other is VBaseMessage && localId == other.localId;

  @override
  int get hashCode => localId.hashCode;
}
