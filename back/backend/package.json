{"name": "new_superup_api", "version": "1.0.1", "description": "", "author": "", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && npm run copy:assets", "copy:assets": "copyfiles firebase-adminsdk.json AuthKey.p8 .env.production .env.development ecosystem.config.js images/* dist", "start": "cross-env INLINE_RUNTIME_CHUNK=false NODE_ENV=production nest start --watch", "start:docker": "nest start", "docker": "INLINE_RUNTIME_CHUNK=false nest start", "start:dev": "cross-env INLINE_RUNTIME_CHUNK=false NODE_ENV=development nest start --watch", "start:prod": "cross-env INLINE_RUNTIME_CHUNK=false NODE_ENV=production node dist/src/main.js", "pm2": "cross-env INLINE_RUNTIME_CHUNK=false NODE_ENV=production pm2 start dist/src/main.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.321.1", "@aws-sdk/s3-request-presigner": "^3.327.0", "@nestjs-modules/mailer": "^1.7.1", "@nestjs/axios": "^1.0.1", "@nestjs/common": "^9.4.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.4.0", "@nestjs/event-emitter": "^1.4.1", "@nestjs/jwt": "^10.0.3", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^9.2.2", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "^9.4.0", "@nestjs/schedule": "^2.2.1", "@nestjs/throttler": "^4.0.0", "@nestjs/websockets": "^9.4.0", "@socket.io/admin-ui": "^0.5.1", "@types/utf8": "^3.0.1", "agora-token": "^2.0.3", "app-root-path": "^3.1.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "class-sanitizer": "^1.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "date-and-time": "^3.0.0", "date-fns": "^2.29.3", "file-type": "^16.5.3", "firebase-admin": "^11.11.0", "geoip-lite": "^1.4.6", "helmet": "^6.1.5", "image-size": "^1.0.2", "link-preview-js": "^3.0.5", "mongoose": "^7.0.4", "mongoose-aggregate-paginate-v2": "^1.0.6", "mongoose-paginate-v2": "^1.7.1", "morgan": "^1.10.0", "nodemailer": "^6.7.5", "onesignal-api-client-core": "^1.2.2", "onesignal-api-client-nest": "^1.0.10", "onesignal-node": "^3.4.0", "os": "^0.1.2", "parse-env-to-json": "^1.0.4", "redis": "^4.5.1", "reflect-metadata": "^0.1.13", "remove-accents": "^0.4.4", "request-ip": "^3.3.0", "rimraf": "^5.0.0", "rxjs": "^7.8.0", "semver": "^7.3.8", "sharp": "^0.32.0", "socket.io": "^4.6.1", "typescript": "4.8.4", "utf8": "^3.0.0", "uuid": "^9.0.0", "xss-clean": "^0.1.1", "@parse/node-apn": "^6.3.0"}, "devDependencies": {"@nestjs/cli": "^9.4.2", "@nestjs/schematics": "^9.1.0", "@nestjs/testing": "^9.4.0", "@types/app-root-path": "^1.2.4", "@types/body-parser": "^1.19.2", "@types/cron": "^2.0.1", "@types/express": "^4.17.17", "@types/geoip-lite": "^1.4.1", "@types/mongoose-aggregate-paginate-v2": "^1.0.7", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.9", "@types/node": "^18.16.0", "@types/nodemailer": "^6.4.7", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/readline-sync": "^1.4.4", "@types/request-ip": "^0.0.37", "@types/semver": "^7.3.13", "@types/sharp": "^0.31.1", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.1", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "4.8.4", "webpack": "^5.80.0", "webpack-node-externals": "^3.0.0"}}